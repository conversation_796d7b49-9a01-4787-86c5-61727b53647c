// Переключатель API: false - тестовый API, true - основной API
const USE_PRODUCTION_API = true;

// Конфигурация для тестового API
const TEST_API_CONFIG = {
  BASE_URL: "http://test.api.sadi.kz",
  IMAGES_BASE_URL: "https://test.sadi.kz",
  TOKEN_URL: null, // Тестовый API не требует токена
  AUTH_REQUIRED: false,
};

// Конфигурация для основного API
const PRODUCTION_API_CONFIG = {
  BASE_URL: "https://api.sadi.kz",
  IMAGES_BASE_URL: "https://sadi.kz",
  TOKEN_URL: "https://api.sadi.kz/Token",
  AUTH_REQUIRED: true,
  AUTH_CREDENTIALS: {
    grant_type: "password",
    username: "<EMAIL>",
    password: "310312",
  },
  TOKEN_LIFETIME_DAYS: 14,
};

// Выбираем конфигурацию в зависимости от переключателя
const CURRENT_CONFIG = USE_PRODUCTION_API
  ? PRODUCTION_API_CONFIG
  : TEST_API_CONFIG;

// Основная конфигурация API
const API_CONFIG = {
  // Переключатель API
  USE_PRODUCTION_API,

  // Базовый URL API
  BASE_URL: CURRENT_CONFIG.BASE_URL,

  // Пути к API endpoints
  ENDPOINTS: {
    PRODUCTS: "/api/Materials",
    PRODUCT_DETAILS: "/api/Materials/:id",
    CATALOG: "/api/MaterialTrees",
    PHOTOS: "/api/MaterialPhotoes",
  },

  // Базовый URL для изображений
  IMAGES_BASE_URL: CURRENT_CONFIG.IMAGES_BASE_URL,

  // URL для получения токена (только для основного API)
  TOKEN_URL: CURRENT_CONFIG.TOKEN_URL,

  // Требуется ли авторизация
  AUTH_REQUIRED: CURRENT_CONFIG.AUTH_REQUIRED,

  // Данные для авторизации (только для основного API)
  AUTH_CREDENTIALS: CURRENT_CONFIG.AUTH_CREDENTIALS,

  // Срок жизни токена в днях
  TOKEN_LIFETIME_DAYS: CURRENT_CONFIG.TOKEN_LIFETIME_DAYS,

  // Настройки запросов
  REQUEST_OPTIONS: {
    headers: {
      "Content-Type": "application/json",
      // Заголовки авторизации будут добавлены динамически при необходимости
    },
  },
};

// Функция для обновления учетных данных авторизации
export const updateAuthCredentials = (username, password) => {
  if (PRODUCTION_API_CONFIG.AUTH_CREDENTIALS) {
    PRODUCTION_API_CONFIG.AUTH_CREDENTIALS.username = username;
    PRODUCTION_API_CONFIG.AUTH_CREDENTIALS.password = password;

    // Обновляем текущую конфигурацию, если используется production API
    if (USE_PRODUCTION_API && API_CONFIG.AUTH_CREDENTIALS) {
      API_CONFIG.AUTH_CREDENTIALS.username = username;
      API_CONFIG.AUTH_CREDENTIALS.password = password;
    }

    console.log("Учетные данные авторизации обновлены");
  }
};

export default API_CONFIG;
