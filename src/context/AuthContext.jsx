import React, { createContext, useState, useContext, useEffect } from "react";
import API_CONFIG from "../config/api.js";
import authService from "../services/auth.service.js";

// Создаем контекст для авторизации
const AuthContext = createContext();

// Хук для использования контекста авторизации
export const useAuth = () => {
  return useContext(AuthContext);
};

// Провайдер контекста авторизации
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authError, setAuthError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Проверяем сохраненный токен при инициализации
  useEffect(() => {
    const savedToken = localStorage.getItem("api_access_token");
    const savedUser = localStorage.getItem("user_data");

    if (savedToken && savedUser) {
      try {
        const userData = JSON.parse(savedUser);
        setUser(userData);
        setIsAuthenticated(true);
        authService.setToken(savedToken);
        console.log("Восстановлена авторизация из localStorage");
      } catch (error) {
        console.error("Ошибка при восстановлении авторизации:", error);
        localStorage.removeItem("api_access_token");
        localStorage.removeItem("user_data");
      }
    }
  }, []);

  // Функция входа в систему
  const login = async (email, password, rememberMe = false) => {
    setIsLoading(true);
    setAuthError("");

    try {
      // Обновляем конфигурацию API с введенными данными
      API_CONFIG.AUTH_CREDENTIALS.username = email;
      API_CONFIG.AUTH_CREDENTIALS.password = password;

      // Пытаемся получить токен
      const token = await authService.getAccessToken();

      if (token) {
        // Успешная авторизация
        const userData = { email };
        setUser(userData);
        setIsAuthenticated(true);
        setAuthError("");

        // Если выбрано "Запомнить меня", сохраняем в localStorage
        if (rememberMe) {
          localStorage.setItem("api_access_token", token);
          localStorage.setItem("user_data", JSON.stringify(userData));
          console.log("Токен и данные пользователя сохранены в localStorage");
        }

        console.log("Успешная авторизация для:", email);
        return true;
      } else {
        // Ошибка получения токена
        setAuthError(
          "Сначала зарегистрируйтесь или неправильно введены данные"
        );
        return false;
      }
    } catch (error) {
      console.error("Ошибка авторизации:", error);
      setAuthError("Сначала зарегистрируйтесь или неправильно введены данные");
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Функция выхода из системы
  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    setAuthError("");
    authService.clearToken();

    // Очищаем данные из localStorage
    localStorage.removeItem("api_access_token");
    localStorage.removeItem("user_data");

    // Очищаем данные из конфигурации
    API_CONFIG.AUTH_CREDENTIALS.username = "";
    API_CONFIG.AUTH_CREDENTIALS.password = "";

    console.log("Пользователь вышел из системы");
  };

  // Очистка ошибки
  const clearError = () => {
    setAuthError("");
  };

  // Значение контекста
  const value = {
    user,
    isAuthenticated,
    authError,
    isLoading,
    login,
    logout,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
