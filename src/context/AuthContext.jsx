import React, { createContext, useState, useContext } from "react";
import API_CONFIG from "../config/api.js";
import authService from "../services/auth.service.js";

// Создаем контекст для авторизации
const AuthContext = createContext();

// Хук для использования контекста авторизации
export const useAuth = () => {
  return useContext(AuthContext);
};

// Провайдер контекста авторизации
export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [authError, setAuthError] = useState("");
  const [isLoading, setIsLoading] = useState(false);

  // Функция входа в систему
  const login = async (email, password) => {
    setIsLoading(true);
    setAuthError("");

    try {
      // Обновляем конфигурацию API с введенными данными
      API_CONFIG.AUTH_CREDENTIALS.username = email;
      API_CONFIG.AUTH_CREDENTIALS.password = password;

      // Пытаемся получить токен
      const token = await authService.getAccessToken();

      if (token) {
        // Успешная авторизация
        setUser({ email });
        setIsAuthenticated(true);
        setAuthError("");
        console.log("Успешная авторизация для:", email);
        return true;
      } else {
        // Ошибка получения токена
        setAuthError("Сначала зарегистрируйтесь или неправильно введены данные");
        return false;
      }
    } catch (error) {
      console.error("Ошибка авторизации:", error);
      setAuthError("Сначала зарегистрируйтесь или неправильно введены данные");
      return false;
    } finally {
      setIsLoading(false);
    }
  };

  // Функция выхода из системы
  const logout = () => {
    setUser(null);
    setIsAuthenticated(false);
    setAuthError("");
    authService.clearToken();
    
    // Очищаем данные из конфигурации
    API_CONFIG.AUTH_CREDENTIALS.username = "";
    API_CONFIG.AUTH_CREDENTIALS.password = "";
    
    console.log("Пользователь вышел из системы");
  };

  // Очистка ошибки
  const clearError = () => {
    setAuthError("");
  };

  // Значение контекста
  const value = {
    user,
    isAuthenticated,
    authError,
    isLoading,
    login,
    logout,
    clearError,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
};
