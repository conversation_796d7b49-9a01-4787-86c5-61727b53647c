import API_CONFIG from "../config/api.js";

/**
 * Сервис для работы с авторизацией и токенами
 */
class AuthService {
  constructor() {
    this.currentToken = null;
  }

  /**
   * Получить токен доступа
   * @returns {Promise<string|null>} - Токен доступа или null
   */
  async getAccessToken() {
    // Если авторизация не требуется, возвращаем null
    if (!API_CONFIG.AUTH_REQUIRED) {
      return null;
    }

    // Если есть токен в памяти, используем его
    if (this.currentToken) {
      console.log("Используется токен из памяти");
      return this.currentToken;
    }

    // Получаем новый токен
    console.log("Получение нового токена...");
    return await this.requestNewToken();
  }

  /**
   * Запросить новый токен с сервера
   * @returns {Promise<string|null>} - Новый токен или null при ошибке
   */
  async requestNewToken() {
    try {
      if (!API_CONFIG.TOKEN_URL || !API_CONFIG.AUTH_CREDENTIALS) {
        throw new Error("Не настроены параметры авторизации");
      }

      // Подготавливаем данные для запроса токена
      const formData = new URLSearchParams();
      Object.entries(API_CONFIG.AUTH_CREDENTIALS).forEach(([key, value]) => {
        formData.append(key, value);
      });

      console.log(`Запрос токена: ${API_CONFIG.TOKEN_URL}`);

      const response = await fetch(API_CONFIG.TOKEN_URL, {
        method: "POST",
        headers: {
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`);
      }

      const data = await response.json();

      if (!data.access_token) {
        throw new Error("Токен не получен от сервера");
      }

      // Сохраняем токен в памяти
      this.currentToken = data.access_token;

      console.log("Токен успешно получен и сохранен в памяти");
      return data.access_token;
    } catch (error) {
      console.error("Ошибка при получении токена:", error);
      this.clearToken();
      return null;
    }
  }

  /**
   * Очистить токен из памяти
   */
  clearToken() {
    this.currentToken = null;
    console.log("Токен очищен из памяти");
  }

  /**
   * Получить заголовки авторизации для запросов
   * @returns {Promise<Object>} - Объект с заголовками
   */
  async getAuthHeaders() {
    if (!API_CONFIG.AUTH_REQUIRED) {
      return {};
    }

    const token = await this.getAccessToken();
    if (!token) {
      console.warn("Не удалось получить токен авторизации");
      return {};
    }

    return {
      Authorization: `Bearer ${token}`,
    };
  }

  /**
   * Проверить, требуется ли авторизация
   * @returns {boolean} - true, если требуется авторизация
   */
  isAuthRequired() {
    return API_CONFIG.AUTH_REQUIRED;
  }

  /**
   * Получить информацию о текущем состоянии авторизации
   * @returns {Object} - Информация о состоянии авторизации
   */
  getAuthStatus() {
    if (!API_CONFIG.AUTH_REQUIRED) {
      return {
        required: false,
        authenticated: true,
        message: "Авторизация не требуется (тестовый API)",
      };
    }

    const hasToken = !!this.currentToken;

    return {
      required: true,
      authenticated: hasToken,
      hasToken: hasToken,
      tokenValid: hasToken,
      message: hasToken ? "Авторизован" : "Токен отсутствует",
    };
  }
}

// Создаем единственный экземпляр сервиса
const authService = new AuthService();

export default authService;
